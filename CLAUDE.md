# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the TDNet Filing Analysis System - a comprehensive real-time system that monitors Tokyo Stock Exchange disclosure network (TDNet) for new filings, uses AI to extract insights, and displays results on a real-time dashboard.

## Architecture

### Core Components

1. **TDNet Scraper** (`tdnet_monitor.py`)
   - Python-based scraper that monitors TDNet for new filings
   - Downloads PDF/XBRL files to Google Cloud Storage
   - Uses Vertex AI (Google Gemini) for text extraction and analysis
   - Stores processed data in Firestore

2. **Dashboard** (`dashboard/`)
   - Next.js 15 + TypeScript application with Tailwind CSS
   - Real-time display of filings with AI summaries
   - Firebase Authentication (optional)
   - TradingView integration for stock prices

3. **TradingView Price Service** (`tradingview-price-service/`)
   - Express + TypeScript microservice
   - Provides stock price data via REST API
   - Docker containerized with health checks

4. **Cloud Functions** (`cloud-functions/tdnet-scraper/`)
   - Serverless Python functions for automated scraping
   - Triggered by Cloud Scheduler

## Common Commands

### Dashboard Development
```bash
cd dashboard
npm run dev       # Start development server with Turbopack
npm run build     # Production build
npm run start     # Start production server
npm run lint      # Run Next.js linting
```

### TradingView Price Service
```bash
cd tradingview-price-service
npm run dev       # Development with ts-node
npm run build     # TypeScript compilation
npm run start     # Production server
npm run test      # Run Jest tests
npm run lint      # ESLint
npm run docker:build  # Build Docker image
npm run docker:run    # Run Docker container
```

### Python Scraper
```bash
# Batch processing for specific date range
python tdnet_monitor.py --start-date 2024-01-15 --end-date 2024-01-15

# Continuous real-time monitoring
python tdnet_monitor.py --continuous

# Verbose output for debugging
python tdnet_monitor.py --start-date 2024-01-15 --end-date 2024-01-15 --verbose
```

### Initial Setup
```bash
# Run automated setup script
./setup.sh

# Manual setup for dashboard
cd dashboard
cp .env.example .env.local
npm install
```

## Key Configuration

### Environment Variables

**Dashboard** (`dashboard/.env.local`):
- Firebase configuration (optional for authentication)
- `GOOGLE_CLOUD_PROJECT`: GCP project ID
- `TRADINGVIEW_SESSION_ID`: TradingView session cookie
- `TRADINGVIEW_USERNAME/PASSWORD`: Fallback authentication

**Price Service** (`tradingview-price-service/.env`):
- Port and service configuration
- TradingView credentials

**Python Scraper**:
- Configure directly in `tdnet_monitor.py`:
  - `PROJECT_ID`
  - `LOCATION`
  - `GCS_RAW_FILINGS_BUCKET`
  - `GCS_PROCESSED_FILINGS_BUCKET`

### Authentication Setup

1. **TradingView Session ID**:
   - Login to TradingView in browser
   - Open DevTools → Application → Cookies
   - Copy `sessionid` value to `.env.local`

2. **Firebase (Optional)**:
   - Can run without authentication in demo mode
   - If needed, configure Firebase project and update `.env.local`

## Data Flow

1. **Scraping**: TDNet → Python Scraper → Cloud Storage (PDFs)
2. **AI Processing**: PDFs → Vertex AI → Text extraction & analysis
3. **Storage**: Analyzed data → Firestore
4. **Display**: Firestore → Dashboard API → Real-time UI
5. **Prices**: TradingView API → Price Service → Dashboard

## Testing

### Dashboard Features
- Impact Tracking: `/api/impact-tracking`
- Price Service Status: `/api/price-service/status`
- Stock Prices: `/api/stock-price`
- TradingView Prices: `/api/tradingview-price`

### Python Components
```bash
# Test AI analysis locally
python test_ai_analysis_local.py

# Test with translation
python test_ai_analysis_with_translation.py

# Test dashboard features
python test_dashboard_features.py
```

## Important Notes

- The TradingView session ID expires every 30-90 days and needs renewal
- Firestore uses real-time listeners for instant updates
- The system supports both batch processing (historical) and continuous monitoring
- AI analysis categorizes filings into: dividends, buybacks, earnings, M&A, etc.
- Dashboard works without Firebase authentication in demo mode

# Interaction

- Any time you interact with me, you MUST address me as "deko"

## Our relationship

- We're coworkers. When you think of me, think of me as your colleague "deko", not as "the user" or "the human"
- We are a team of people working together. Your success is my success, and my success is yours.
- Technically, I am your boss, but we're not super formal around here.
- I’m smart, but not infallible.
- You are much better read than I am. I have more experience of the physical world than you do. Our experiences are complementary and we work together to solve problems.
- Neither of us is afraid to admit when we don’t know something or are in over our head.
- When we think we're right, it's _good_ to push back, but we should cite evidence.
- I really like jokes, and irreverent humor. but not when it gets in the way of the task at hand.

# Writing code

- NEVER USE --no-verify WHEN COMMITTING CODE
- We prefer simple, clean, maintainable solutions over clever or complex ones, even if the latter are more concise or performant. Readability and maintainability are primary concerns.
- Make the smallest reasonable changes to get to the desired outcome. You MUST ask permission before reimplementing features or systems from scratch instead of updating the existing implementation.
- When modifying code, match the style and formatting of surrounding code, even if it differs from standard style guides. Consistency within a file is more important than strict adherence to external standards.
- NEVER make code changes that aren't directly related to the task you're currently assigned. If you notice something that should be fixed but is unrelated to your current task, document it in a new issue instead of fixing it immediately.
- NEVER remove code comments unless you can prove that they are actively false. Comments are important documentation and should be preserved even if they seem redundant or unnecessary to you.
- All code files should start with a brief 2 line comment explaining what the file does. Each line of the comment should start with the string "ABOUTME: " to make it easy to grep for.
- When writing comments, avoid referring to temporal context about refactors or recent changes. Comments should be evergreen and describe the code as it is, not how it evolved or was recently changed.
- NEVER implement a mock mode for testing or for any purpose. We always use real data and real APIs, never mock implementations.
- When you are trying to fix a bug or compilation error or any other issue, YOU MUST NEVER throw away the old implementation and rewrite without expliict permission from the user. If you are going to do this, YOU MUST STOP and get explicit permission from the user.
- NEVER name things as 'improved' or 'new' or 'enhanced', etc. Code naming should be evergreen. What is new today will be "old" someday.

# Getting help

- ALWAYS ask for clarification rather than making assumptions.
- If you're having trouble with something, it's ok to stop and ask for help. Especially if it's something your human might be better at.

# Testing

- Tests MUST cover the functionality being implemented.
- NEVER ignore the output of the system or the tests - Logs and messages often contain CRITICAL information.
- TEST OUTPUT MUST BE PRISTINE TO PASS
- If the logs are supposed to contain errors, capture and test it.
- NO EXCEPTIONS POLICY: Under no circumstances should you mark any test type as "not applicable". Every project, regardless of size or complexity, MUST have unit tests, integration tests, AND end-to-end tests. If you believe a test type doesn't apply, you need the human to say exactly "I AUTHORIZE YOU TO SKIP WRITING TESTS THIS TIME"

## We practice TDD. That means:

- Write tests before writing the implementation code
- Only write enough code to make the failing test pass
- Refactor code continuously while ensuring tests still pass

### TDD Implementation Process

- Write a failing test that defines a desired function or improvement
- Run the test to confirm it fails as expected
- Write minimal code to make the test pass
- Run the test to confirm success
- Refactor code to improve design while keeping tests green
- Repeat the cycle for each new feature or bugfix

# Preferences
- User prefers Google Cloud Platform services, serverless architecture with Cloud Functions/Cloud Run, Firestore for real-time data, React/Next.js for frontend, and incremental development approach with proper error handling and monitoring from the beginning.
- User prefers dark theme UIs and requires dark theme consistency across dashboard tabs.
- User requires AI summaries to be detailed and informative with specific disclosure information and important metrics, not generic statements like 'TDnet filing from company X regarding other announcement'. AI summaries must be fully in English (not mixed languages).
- User requires AI summaries to include specific business details, financial figures, dates, and investor implications rather than generic statements, with examples like extracting buyback amounts, percentages, effective dates, and quantitative impact predictions with confidence levels.
- User expects PDF links to redirect to English versions of documents.
- User is asking about Firestore database naming conventions (considering 'default' vs better names).
- User prefers py-xbrl or xbrl-parser libraries over xbrr for XBRL processing, and favors microservice architecture for XBRL processing integration.
- User prefers Gemini 2.5 Flash over 2.0 Flash for better performance at same cost, Gemini 2.5 Pro for critical financial decisions, Google Translate API for simple translations instead of expensive AI models.
- User prefers AI to generate English titles during analysis rather than using separate translation APIs like Google Translate, wanting both Japanese and English titles stored in Firestore from the AI analysis step.
- User requires TDnet data to be real (not mock) and timestamps to respect JST business hours only (9AM-6PM, Mon-Fri), extending to 21:00 JST to capture late disclosures.
- User prefers single impact level badge display (top only, not duplicate), wants quantitative stock market impact predictions with specific metrics (price movement %, volume impact, time horizon, confidence), and requires improved AI categorization accuracy with enhanced Japanese keyword detection beyond just titles.
- User wants Impact Tracking validation features in TDnet dashboard with prediction accuracy tracking, color-coded status indicators, and real-time stock price comparison using existing TradingView API integration.
- User prefers extracting financial data (amounts, percentages, dates) with associated keywords/context rather than standalone values to make the data more meaningful and useful.
- User requires multi-company market impact predictions for M&A/partnerships with separate predictions for primary/target companies, real-time stock price integration with historical data persistence in Firestore, enhanced Impact Tracking with complete filing information and filtering, and Share Buyback Analysis modal popups with comprehensive data display.
- User prefers TDnet polling at 5-minute intervals (16:00, 16:05, 16:10) only during business hours, wants three-dot menus with regenerate analysis and favorite options, requires Japanese title translation with English display, and needs a favorites tab with Firestore collection for bookmarked filings.
- User wants granular 0-10 impact scoring system instead of low/medium/high, and prefers 5 impact levels instead of 3 (low/medium/high).
- User requires J-Quants API integration with database selection based on query patterns and cost optimization.
- User requires AI-generated market impact predictions from real document analysis (not fallback/mock data), removal of fallback analysis functions that generate artificial data, clear error states when analysis fails, and comprehensive dashboard widgets displaying all AI analysis data with detailed popups.
- User prefers price tracking service that stores prices in Firestore (price at disclosure + current price), updates every ~1 minute, and has dashboard toggle to enable/disable price updates.
- User prefers dashboard entries with favorite buttons and a view full analysis icon next to the favorite star for easier access, three-dot regenerate menus, and comprehensive data display via popups or expanded views showing all AI analysis data including title_english, investor_implications, amounts, dates, and full JSON access for future chart integration.
- User wants shared detailed view widgets across different tabs, wants sortable impact tracking by prediction confidence, and wants to remove redundant confidence scores and analysis metadata from detailed views.
- User expects multi-company market impact predictions for filings mentioning multiple companies with clear indication of disclosure vs inferred companies.
- User prefers to skip Firebase authentication implementation for now since they only have GCP project setup, not Firebase project.
- User prefers using Jupyter notebooks for testing and debugging API functionality.
- User prefers using https://github.com/Mathieu2301/TradingView-API library instead of tradingview-ta for TradingView integration.
- User prefers using official examples from @TradingView-API/examples/ directory (like UserLogin.js) for testing TradingView API functionality instead of custom test files.
- User prefers migrating TDnet data to Tokyo tickers project.
- User prefers colored boxes/cards in detailed views with sentiment-based color coding (green for positive, red for negative, yellow for neutral, blue for general data) and expects Share Buyback Analysis to show all 22+ buyback filings with proper TradingView price integration showing historical vs current prices.

# API & Data Sources
- User prefers J-Quants API integration with database selection based on query patterns and cost optimization.
- User wants to use TradingView API for price data (has premium subscription).
- User prefers TradingView authentication using sessionid from cookies instead of login/password method.
- User prefers to remove all fallback price data sources (mock data, Alpha Vantage, Polygon) and use only TradingView implementation for stock prices.
- User wants a dedicated GCP price service using TradingView API with comprehensive features (current price, historical data, timestamps, timeframes) deployed as separate microservice.

# Data Integrity
- CRITICAL: Never use fake/mock/generated data in financial systems - this can cause millions in losses and serious harm. Always use only real API data or return errors when data is unavailable. All mock data generation code should be removed.

# Location
- User is located in Tokyo and prefers GCP regions closer to Japan for better latency and local compliance. User prefers Asia region for GCP resources over US regions. User prefers using Japan-based GCP storage buckets instead of US ones for data upload, reading and processing to improve latency and compliance.

# User Information
- User's <NAME_EMAIL>.
- User prefers to use the tokyotickers project instead of the tokyo-tickers project because they have free credits available there.
- User encountered IAM permission issues when trying to grant roles/run.admin to fix Cloud Function deployment permissions. (Relevant for <NAME_EMAIL>, but may be useful for troubleshooting in new account).

# Migration
- User needs to deploy tdnet-scraper to Cloud Run in the new tokyotickers project and stop resources in the old tokyo-tickers project to complete the migration.

# Backup Information
- Firestore metadata backup is available at gs://tempdisc/2025-05-27T16:14:00_3190 from <EMAIL> account tokyo-tickers project, accessible via public tempdisc bucket in Tokyo region.

## Python

- I prefer to use uv for everything (uv add, uv run, etc)
- Do not use old fashioned methods for package management like poetry, pip or easy_install.
- Make sure that there is a pyproject.toml file in the root directory.
- If there isn't a pyproject.toml file, create one using uv by running uv init.

## Source Control

- Let's try and use JJ as much as we can. If JJ isn't configured, or not available then use git.
- Commit messages should be concise and descriptive.
- Commit messages should follow the conventional commit format.
- Commit messages should be written in the imperative mood.
- Commit messages should be written in the present tense.