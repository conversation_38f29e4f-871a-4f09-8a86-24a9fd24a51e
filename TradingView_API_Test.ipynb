# Install required packages for Python (for data analysis)
!pip install pandas requests

# Check if Node.js is available
!node --version
!npm --version

# Install the TradingView library
!npm install @mathieuc/tradingview

# Configuration - UPDATE THESE WITH YOUR CREDENTIALS
import pandas as pd
import json
import time
from datetime import datetime

# Your TradingView credentials
TRADINGVIEW_SESSION_ID = "3tm8llrurd03b0sybkf0gxl5wszlyuu7"  # Update with your session ID
TRADINGVIEW_USERNAME = "<EMAIL>"  # Update with your username
TRADINGVIEW_PASSWORD = "Nutsu@747"  # Update with your password

# Test symbols (Japanese stocks)
TEST_SYMBOLS = [
    'TSE:7203',  # Toyota
    'TSE:6758',  # Sony
    'TSE:9984',  # SoftBank
    'TSE:3664',  # Mobcast Holdings
    'TSE:6135'   # Makino Milling
]

print("✅ Configuration loaded")
print(f"Session ID: {TRADINGVIEW_SESSION_ID[:10]}...")
print(f"Username: {TRADINGVIEW_USERNAME}")
print(f"Test symbols: {TEST_SYMBOLS}")

# Create JavaScript test files for TradingView API

# Test 1: Authentication Test
auth_test_js = f'''
const TradingView = require('@mathieuc/tradingview');

async function testAuthentication() {{
    console.log('🔐 Testing TradingView Authentication...');
    
    const sessionId = '{TRADINGVIEW_SESSION_ID}';
    const username = '{TRADINGVIEW_USERNAME}';
    const password = '{TRADINGVIEW_PASSWORD}';
    
    try {{
        console.log('\n1. Testing Session ID Authentication...');
        const userBySession = await TradingView.getUser(sessionId);
        console.log('✅ Session ID authentication successful!');
        console.log('User info:', {{
            username: userBySession.username,
            id: userBySession.id,
            session: userBySession.session.substring(0, 10) + '...'
        }});
        return {{ success: true, method: 'session', user: userBySession }};
    }} catch (sessionError) {{
        console.log('❌ Session ID authentication failed:', sessionError.message);
        
        try {{
            console.log('\n2. Testing Username/Password Authentication...');
            const userByLogin = await TradingView.loginUser(username, password);
            console.log('✅ Username/Password authentication successful!');
            console.log('User info:', {{
                username: userByLogin.username,
                id: userByLogin.id,
                session: userByLogin.session.substring(0, 10) + '...'
            }});
            return {{ success: true, method: 'login', user: userByLogin }};
        }} catch (loginError) {{
            console.log('❌ Username/Password authentication failed:', loginError.message);
            return {{ success: false, sessionError: sessionError.message, loginError: loginError.message }};
        }}
    }}
}}

testAuthentication().then(result => {{
    console.log('\n📋 Authentication Result:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
}}).catch(error => {{
    console.error('💥 Unexpected error:', error);
    process.exit(1);
}});
'''

with open('test_auth.js', 'w') as f:
    f.write(auth_test_js)

print("✅ Created test_auth.js")

# Run the authentication test
!node test_auth.js

# Create Quote Data Test
quote_test_js = f'''
const TradingView = require('@mathieuc/tradingview');

async function testQuoteData() {{
    console.log('📈 Testing TradingView Quote Data...');
    
    const sessionId = '{TRADINGVIEW_SESSION_ID}';
    const testSymbols = ['TSE:7203', 'TSE:6758', 'TSE:9984', 'TSE:3664', 'TSE:6135'];
    
    let user = null;
    
    // Try to authenticate first
    try {{
        user = await TradingView.getUser(sessionId);
        console.log('✅ Authenticated with session ID');
    }} catch (error) {{
        console.log('⚠️ Session authentication failed, trying anonymous access');
    }}
    
    // Create client
    const clientOptions = {{}};
    if (user) {{
        clientOptions.token = user.session;
        clientOptions.signature = user.signature;
    }}
    
    const client = new TradingView.Client(clientOptions);
    
    return new Promise((resolve, reject) => {{
        const results = [];
        let completedSymbols = 0;
        const timeout = setTimeout(() => {{
            client.end();
            reject(new Error('Test timeout after 30 seconds'));
        }}, 30000);
        
        client.onError((error) => {{
            clearTimeout(timeout);
            client.end();
            reject(error);
        }});
        
        client.onConnected(() => {{
            console.log('🔗 Connected to TradingView');
            
            const quoteSession = new client.Session.Quote();
            
            testSymbols.forEach((symbol, index) => {{
                console.log(`Testing symbol: ${{symbol}}`);
                
                const market = new quoteSession.Market(symbol);
                
                market.onError((error) => {{
                    console.error(`❌ Error for ${{symbol}}:`, error.message);
                    results.push({{
                        symbol,
                        status: 'error',
                        error: error.message
                    }});
                    
                    completedSymbols++;
                    if (completedSymbols === testSymbols.length) {{
                        clearTimeout(timeout);
                        quoteSession.delete();
                        client.end();
                        resolve(results);
                    }}
                }});
                
                market.onLoaded(() => {{
                    console.log(`✅ ${{symbol}} loaded`);
                }});
                
                market.onData((data) => {{
                    console.log(`📊 ${{symbol}} data:`, {{
                        price: data.lp,
                        change: data.ch,
                        changePercent: data.chp,
                        volume: data.volume,
                        currency: data.currency_code
                    }});
                    
                    results.push({{
                        symbol,
                        status: 'success',
                        price: data.lp,
                        change: data.ch,
                        changePercent: data.chp,
                        volume: data.volume,
                        currency: data.currency_code,
                        timestamp: new Date().toISOString()
                    }});
                    
                    market.close();
                    completedSymbols++;
                    
                    if (completedSymbols === testSymbols.length) {{
                        clearTimeout(timeout);
                        quoteSession.delete();
                        client.end();
                        resolve(results);
                    }}
                }});
            }});
        }});
    }});
}}

testQuoteData().then(results => {{
    console.log('\n📋 Quote Data Results:');
    results.forEach(result => {{
        if (result.status === 'success') {{
            console.log(`✅ ${{result.symbol}}: ¥${{result.price}} (${{result.change > 0 ? '+' : ''}}${{result.change}}, ${{result.changePercent > 0 ? '+' : ''}}${{result.changePercent}}%)`);
        }} else {{
            console.log(`❌ ${{result.symbol}}: ${{result.error}}`);
        }}
    }});
    
    const successCount = results.filter(r => r.status === 'success').length;
    console.log(`\n📊 Summary: ${{successCount}}/${{results.length}} symbols successful`);
    
    process.exit(successCount > 0 ? 0 : 1);
}}).catch(error => {{
    console.error('💥 Quote data test failed:', error.message);
    process.exit(1);
}});
'''

with open('test_quotes.js', 'w') as f:
    f.write(quote_test_js)

print("✅ Created test_quotes.js")

# Run the quote data test
!node test_quotes.js

# Create Chart Data Test (Historical Data)
chart_test_js = f'''
const TradingView = require('@mathieuc/tradingview');

async function testChartData() {{
    console.log('📊 Testing TradingView Chart Data (Historical)...');
    
    const sessionId = '{TRADINGVIEW_SESSION_ID}';
    const testSymbol = 'TSE:7203';  // Toyota
    
    let user = null;
    
    // Try to authenticate first
    try {{
        user = await TradingView.getUser(sessionId);
        console.log('✅ Authenticated with session ID');
    }} catch (error) {{
        console.log('⚠️ Session authentication failed, trying anonymous access');
    }}
    
    // Create client
    const clientOptions = {{}};
    if (user) {{
        clientOptions.token = user.session;
        clientOptions.signature = user.signature;
    }}
    
    const client = new TradingView.Client(clientOptions);
    
    return new Promise((resolve, reject) => {{
        const timeout = setTimeout(() => {{
            client.end();
            reject(new Error('Chart test timeout after 30 seconds'));
        }}, 30000);
        
        client.onError((error) => {{
            clearTimeout(timeout);
            client.end();
            reject(error);
        }});
        
        client.onConnected(() => {{
            console.log('🔗 Connected to TradingView for chart data');
            
            const chart = new client.Session.Chart();
            
            chart.onError((error) => {{
                clearTimeout(timeout);
                chart.delete();
                client.end();
                reject(error);
            }});
            
            chart.onSymbolLoaded(() => {{
                console.log(`✅ Chart symbol loaded: ${{chart.infos.description}}`);
            }});
            
            chart.onUpdate(() => {{
                if (!chart.periods || chart.periods.length === 0) return;
                
                const latestPeriod = chart.periods[chart.periods.length - 1];
                console.log(`📈 Latest period:`, {{
                    time: new Date(latestPeriod.time * 1000).toISOString(),
                    open: latestPeriod.open,
                    high: latestPeriod.high,
                    low: latestPeriod.low,
                    close: latestPeriod.close,
                    volume: latestPeriod.volume
                }});
                
                const result = {{
                    symbol: testSymbol,
                    status: 'success',
                    periodsCount: chart.periods.length,
                    latestPeriod: {{
                        time: new Date(latestPeriod.time * 1000).toISOString(),
                        open: latestPeriod.open,
                        high: latestPeriod.high,
                        low: latestPeriod.low,
                        close: latestPeriod.close,
                        volume: latestPeriod.volume
                    }},
                    info: chart.infos
                }};
                
                clearTimeout(timeout);
                chart.delete();
                client.end();
                resolve(result);
            }});
            
            // Set market to Toyota with daily timeframe
            chart.setMarket(testSymbol, {{
                timeframe: 'D'
            }});
        }});
    }});
}}

testChartData().then(result => {{
    console.log('\n📋 Chart Data Result:');
    if (result.status === 'success') {{
        console.log(`✅ ${{result.symbol}}: Got ${{result.periodsCount}} periods`);
        console.log(`   Latest: ${{result.latestPeriod.close}} (O: ${{result.latestPeriod.open}}, H: ${{result.latestPeriod.high}}, L: ${{result.latestPeriod.low}})`);
        console.log(`   Volume: ${{result.latestPeriod.volume}}`);
        console.log(`   Time: ${{result.latestPeriod.time}}`);
    }} else {{
        console.log(`❌ ${{result.symbol}}: ${{result.error}}`);
    }}
    
    console.log('\n📊 Chart test completed successfully!');
    process.exit(0);
}}).catch(error => {{
    console.error('💥 Chart data test failed:', error.message);
    process.exit(1);
}});
'''

with open('test_chart.js', 'w') as f:
    f.write(chart_test_js)

print("✅ Created test_chart.js")

# Run the chart data test
!node test_chart.js

def test_yahoo_finance_fallback():
    """Test Yahoo Finance as fallback data source"""
    print("📈 Testing Yahoo Finance Fallback...\n")
    
    results = []
    
    # Convert TSE symbols to Yahoo Finance format
    yahoo_symbols = [symbol.replace('TSE:', '') + '.T' for symbol in TEST_SYMBOLS]
    
    for i, yahoo_symbol in enumerate(yahoo_symbols):
        try:
            original_symbol = TEST_SYMBOLS[i]
            
            # Yahoo Finance API endpoint
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}?interval=1d&range=1d"
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('chart', {}).get('result'):
                    meta = data['chart']['result'][0]['meta']
                    current_price = meta.get('regularMarketPrice', meta.get('previousClose', 0))
                    previous_close = meta.get('previousClose', 0)
                    currency = meta.get('currency', 'JPY')
                    
                    change = current_price - previous_close if previous_close else 0
                    change_percent = (change / previous_close * 100) if previous_close else 0
                    
                    result = {
                        'Original Symbol': original_symbol,
                        'Yahoo Symbol': yahoo_symbol,
                        'Price': current_price,
                        'Currency': currency,
                        'Change': round(change, 2),
                        'Change %': round(change_percent, 2),
                        'Status': '✅ Success'
                    }
                    
                    print(f"✅ {original_symbol} ({yahoo_symbol}): ¥{current_price:,.0f} ({change:+.2f}, {change_percent:+.2f}%)")
                    
                else:
                    result = {
                        'Original Symbol': original_symbol,
                        'Yahoo Symbol': yahoo_symbol,
                        'Price': 0,
                        'Currency': 'JPY',
                        'Change': 0,
                        'Change %': 0,
                        'Status': '❌ No data in response'
                    }
                    print(f"❌ {original_symbol} ({yahoo_symbol}): No data in response")
            else:
                result = {
                    'Original Symbol': original_symbol,
                    'Yahoo Symbol': yahoo_symbol,
                    'Price': 0,
                    'Currency': 'JPY',
                    'Change': 0,
                    'Change %': 0,
                    'Status': f'❌ HTTP {response.status_code}'
                }
                print(f"❌ {original_symbol} ({yahoo_symbol}): HTTP {response.status_code}")
                
        except Exception as e:
            result = {
                'Original Symbol': original_symbol,
                'Yahoo Symbol': yahoo_symbol,
                'Price': 0,
                'Currency': 'JPY',
                'Change': 0,
                'Change %': 0,
                'Status': f'❌ Error: {str(e)}'
            }
            print(f"❌ {original_symbol} ({yahoo_symbol}): Error - {str(e)}")
        
        results.append(result)
    
    return pd.DataFrame(results)

# Run the test
yahoo_results = test_yahoo_finance_fallback()
print("\n📊 Yahoo Finance Results:")
display(yahoo_results)

def show_session_id_instructions():
    """Show instructions for getting a fresh TradingView session ID"""
    
    instructions = """
    🔑 How to Get Fresh TradingView Session ID:
    
    1. Open a new browser window/tab
    2. Go to https://www.tradingview.com/
    3. Login to your TradingView account
    4. Open Developer Tools (F12 or right-click → Inspect)
    5. Go to Application tab (Chrome) or Storage tab (Firefox)
    6. Navigate to Cookies → https://tradingview.com
    7. Find the cookie named 'sessionid'
    8. Copy the entire value (should be ~32 characters)
    9. Update the TRADINGVIEW_SESSION_ID variable above
    10. Re-run the authentication tests
    
    ⚠️ Note: Session IDs expire after some time or if you logout
    
    🔄 Alternative: Use username/password authentication
    - Update TRADINGVIEW_USERNAME and TRADINGVIEW_PASSWORD
    - This method is more reliable but slower
    
    📚 Library Documentation:
    - GitHub: https://github.com/Mathieu2301/TradingView-API
    - This is the exact library used in your dashboard
    """
    
    print(instructions)

show_session_id_instructions()

def generate_summary():
    """Generate summary and recommendations based on test results"""
    
    print("📋 TRADINGVIEW API TEST SUMMARY\n")
    print("=" * 60)
    
    print("🧪 TESTS PERFORMED:")
    print("1. ✅ Authentication Test (Session ID + Username/Password)")
    print("2. ✅ Quote Data Test (Real-time prices)")
    print("3. ✅ Chart Data Test (Historical OHLCV data)")
    print("4. ✅ Yahoo Finance Fallback Test")
    print()
    
    print("📚 LIBRARY TESTED:")
    print("   @mathieuc/tradingview - https://github.com/Mathieu2301/TradingView-API")
    print("   This is the EXACT library used in your dashboard")
    print()
    
    print("🎯 WHAT TO LOOK FOR IN RESULTS:")
    print()
    print("✅ AUTHENTICATION SUCCESS:")
    print("   - Session ID or Username/Password works")
    print("   - User info displayed")
    print("   - No timeout or 403 errors")
    print()
    
    print("✅ QUOTE DATA SUCCESS:")
    print("   - Real-time prices for Japanese stocks")
    print("   - Price, change, volume data")
    print("   - Multiple symbols working")
    print()
    
    print("✅ CHART DATA SUCCESS:")
    print("   - Historical OHLCV data")
    print("   - Multiple periods returned")
    print("   - Latest period with valid data")
    print()
    
    print("❌ COMMON ISSUES:")
    print("   - 'read ETIMEDOUT' = Session ID expired")
    print("   - '403 Forbidden' = IP blocked or invalid session")
    print("   - 'Symbol error' = Invalid symbol format")
    print("   - Timeout errors = Network/rate limiting")
    print()
    
    print("🔧 TROUBLESHOOTING:")
    print()
    print("1. 🔑 IF AUTHENTICATION FAILS:")
    print("   - Get fresh session ID from browser")
    print("   - Try username/password instead")
    print("   - Check for typos in credentials")
    print()
    
    print("2. ⏰ IF REQUESTS TIMEOUT:")
    print("   - Wait 10-15 minutes (rate limiting)")
    print("   - Try different network/VPN")
    print("   - Use Yahoo Finance fallback")
    print()
    
    print("3. 🛡️ IF DASHBOARD STILL FAILS:")
    print("   - Update .env.local with working credentials")
    print("   - Restart development server")
    print("   - Check server logs for errors")
    print()
    
    print("📈 DASHBOARD INTEGRATION:")
    print("   - Your dashboard uses the same @mathieuc/tradingview library")
    print("   - Same authentication methods")
    print("   - Same API calls for quotes and charts")
    print("   - Yahoo Finance as automatic fallback")
    print()
    
    print("=" * 60)
    print(f"📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🎉 Run all cells above to test your TradingView API setup!")

# Generate summary
generate_summary()