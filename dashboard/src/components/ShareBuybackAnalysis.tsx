'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, TrendingUp, TrendingDown, Minus, ExternalLink, Eye, X, FileText, Target } from 'lucide-react';
import { stockPriceService, formatPrice, formatPercentage, getPerformanceColor, PriceComparison } from '@/lib/stockPriceAPI';
import { format } from 'date-fns';

interface CompanyMarketImpact {
  company_code: string;
  company_name: string;
  role: 'primary' | 'target' | 'secondary' | 'partner';
  price_movement_range: string;
  volume_impact: string;
  time_horizon: string;
  confidence_level: number;
}

interface Filing {
  id: string;
  company_code: string;
  company_name: string;
  company_name_en?: string;
  title: string;
  disclosure_datetime: string;
  pdf_url: string;
  ai_analysis: {
    category: string;
    summary: string;
    impact_level: string;
    impact_assessment?: string;
    confidence_score?: number;
    key_data?: {
      dividend_amount?: string | null;
      buyback_amount?: string | null;
      revenue?: string | null;
      profit?: string | null;
      effective_date?: string | null;
      announcement_date?: string | null;
      dividend_yield?: string | null;
      shares_to_buyback?: string | null;
    };
    market_impact?: {
      price_movement_range: string;
      volume_impact: string;
      time_horizon: string;
      confidence_level: number;
    };
    multi_company_impact?: CompanyMarketImpact[];
    involved_companies?: {
      company_code: string;
      company_name: string;
      role: string;
    }[];
  };
}

interface BuybackAnalysis extends Filing {
  priceComparison?: PriceComparison | null;
  loading?: boolean;
}

export default function ShareBuybackAnalysis() {
  const [buybackFilings, setBuybackFilings] = useState<BuybackAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshingPrices, setRefreshingPrices] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiling, setSelectedFiling] = useState<BuybackAnalysis | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Fetch share buyback filings
  const fetchBuybackFilings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/filings?category=share_buyback&limit=50');

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch buyback filings');
      }

      const filings: BuybackAnalysis[] = data.data.map((filing: Filing) => ({
        ...filing,
        loading: true
      }));

      setBuybackFilings(filings);

      // Fetch price data for each filing
      await fetchPriceData(filings);

    } catch (err) {
      console.error('Error fetching buyback filings:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch price comparison data
  const fetchPriceData = async (filings: BuybackAnalysis[]) => {
    const companies = filings.map(filing => ({
      code: filing.company_code,
      announcementDate: filing.disclosure_datetime.split('T')[0] // Extract date part
    }));

    try {
      const priceComparisons = await stockPriceService.getBatchPriceComparisons(companies);

      setBuybackFilings(prevFilings =>
        prevFilings.map((filing, index) => ({
          ...filing,
          priceComparison: priceComparisons[index],
          loading: false
        }))
      );

    } catch (err) {
      console.error('Error fetching price data:', err);

      // Mark all as not loading even if price fetch failed
      setBuybackFilings(prevFilings =>
        prevFilings.map(filing => ({
          ...filing,
          loading: false
        }))
      );
    }
  };

  // Manual refresh prices
  const refreshPrices = async () => {
    setRefreshingPrices(true);
    stockPriceService.clearCache(); // Clear cache for fresh data

    const filings = buybackFilings.map(filing => ({ ...filing, loading: true }));
    setBuybackFilings(filings);

    await fetchPriceData(filings);
    setRefreshingPrices(false);
  };

  // Modal functions
  const openModal = (filing: BuybackAnalysis) => {
    setSelectedFiling(filing);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedFiling(null);
  };

  // Load data on component mount
  useEffect(() => {
    fetchBuybackFilings();
  }, [fetchBuybackFilings]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get impact level badge color
  const getImpactColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Render performance indicator
  const renderPerformanceIndicator = (comparison: PriceComparison | null | undefined) => {
    if (!comparison) {
      return <span className="text-gray-500">No data</span>;
    }

    const Icon = comparison.performance === 'gain' ? TrendingUp :
                 comparison.performance === 'loss' ? TrendingDown : Minus;

    return (
      <div className={`flex items-center space-x-2 ${getPerformanceColor(comparison.performance)}`}>
        <Icon className="w-4 h-4" />
        <span className="font-medium">
          {formatPercentage(comparison.percentChange)}
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-400">Loading share buyback analysis...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <h3 className="text-red-400 font-medium mb-2">Error Loading Data</h3>
        <p className="text-gray-300 mb-4">{error}</p>
        <button
          onClick={fetchBuybackFilings}
          className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Share Buyback Analysis</h2>
          <p className="text-gray-400">
            Track stock performance following share buyback announcements
          </p>
        </div>

        <button
          onClick={refreshPrices}
          disabled={refreshingPrices}
          className="flex items-center space-x-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${refreshingPrices ? 'animate-spin' : ''}`} />
          <span>Refresh Prices</span>
        </button>
      </div>

      {/* Summary Stats */}
      {buybackFilings.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm font-medium">Total Buybacks</h3>
            <p className="text-2xl font-bold text-white">{buybackFilings.length}</p>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm font-medium">Positive Performance</h3>
            <p className="text-2xl font-bold text-green-400">
              {buybackFilings.filter(f => f.priceComparison?.performance === 'gain').length}
            </p>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm font-medium">Negative Performance</h3>
            <p className="text-2xl font-bold text-red-400">
              {buybackFilings.filter(f => f.priceComparison?.performance === 'loss').length}
            </p>
          </div>
        </div>
      )}

      {/* Buyback Filings Table */}
      {buybackFilings.length === 0 ? (
        <div className="bg-gray-800/30 rounded-lg p-8 text-center">
          <p className="text-gray-400 text-lg">No share buyback filings found</p>
          <p className="text-gray-500 text-sm mt-2">
            Share buyback announcements will appear here when available
          </p>
        </div>
      ) : (
        <div className="bg-gray-800/30 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-800/50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Announcement
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Historical Price
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Current Price
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Performance
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Impact
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700/50">
                {buybackFilings.map((filing) => (
                  <tr key={filing.id} className="hover:bg-gray-800/20 transition-colors">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-white font-medium">{filing.company_code}</div>
                        <div className="text-gray-400 text-sm">{filing.company_name_en}</div>
                      </div>
                    </td>

                    <td className="px-6 py-4">
                      <div>
                        <div className="text-white text-sm">{formatDate(filing.disclosure_datetime)}</div>
                        <div className="text-gray-400 text-xs mt-1 max-w-xs truncate">
                          {filing.ai_analysis.summary}
                        </div>
                      </div>
                    </td>

                    <td className="px-6 py-4">
                      {filing.loading ? (
                        <div className="animate-pulse bg-gray-700 h-4 w-16 rounded"></div>
                      ) : filing.priceComparison ? (
                        <span className="text-white">
                          {formatPrice(filing.priceComparison.historicalPrice)}
                        </span>
                      ) : (
                        <span className="text-gray-500">No data</span>
                      )}
                    </td>

                    <td className="px-6 py-4">
                      {filing.loading ? (
                        <div className="animate-pulse bg-gray-700 h-4 w-16 rounded"></div>
                      ) : filing.priceComparison ? (
                        <span className="text-white">
                          {formatPrice(filing.priceComparison.currentPrice)}
                        </span>
                      ) : (
                        <span className="text-gray-500">No data</span>
                      )}
                    </td>

                    <td className="px-6 py-4">
                      {filing.loading ? (
                        <div className="animate-pulse bg-gray-700 h-4 w-20 rounded"></div>
                      ) : (
                        renderPerformanceIndicator(filing.priceComparison)
                      )}
                    </td>

                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getImpactColor(filing.ai_analysis.impact_level)}`}>
                        {filing.ai_analysis.impact_level}
                      </span>
                    </td>

                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => openModal(filing)}
                          className="text-purple-400 hover:text-purple-300 transition-colors"
                          title="View Full Analysis"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <a
                          href={filing.pdf_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                          title="View PDF"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Full Analysis Modal */}
      {showModal && selectedFiling && (
        <FullAnalysisModal filing={selectedFiling} onClose={closeModal} />
      )}
    </div>
  );
}

// Full Analysis Modal Component
const FullAnalysisModal: React.FC<{ filing: BuybackAnalysis; onClose: () => void }> = ({ filing, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-white">Full Analysis</h2>
            <span className="text-lg font-semibold text-blue-400">{filing.company_code}</span>
            <span className="text-sm text-gray-300">{filing.company_name}</span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6 space-y-6">
          {/* Filing Information */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-white">{filing.title}</h3>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded text-xs font-medium border ${
                  filing.ai_analysis.impact_level === 'high'
                    ? 'bg-red-500/20 text-red-400 border-red-500/30'
                    : filing.ai_analysis.impact_level === 'medium'
                    ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                    : 'bg-green-500/20 text-green-400 border-green-500/30'
                }`}>
                  {filing.ai_analysis.impact_level.toUpperCase()} IMPACT
                </span>
                <span className="px-2 py-1 rounded text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
                  {filing.ai_analysis.category.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <p className="text-gray-400">
                Disclosed: {format(new Date(filing.disclosure_datetime), 'yyyy-MM-dd HH:mm')} JST
              </p>
              <a
                href={filing.pdf_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-400 bg-blue-500/10 border border-blue-500/30 rounded hover:bg-blue-500/20 transition-colors"
              >
                <FileText className="w-3 h-3 mr-1" />
                View PDF
                <ExternalLink className="w-3 h-3 ml-1" />
              </a>
            </div>
          </div>

          {/* AI Summary */}
          {filing.ai_analysis.summary && (
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-300 mb-2">AI Summary</h4>
              <p className="text-sm text-gray-200">{filing.ai_analysis.summary}</p>
              {filing.ai_analysis.impact_assessment && (
                <p className="text-xs text-gray-400 mt-2 italic">{filing.ai_analysis.impact_assessment}</p>
              )}
              {filing.ai_analysis.confidence_score && (
                <p className="text-xs text-gray-400 mt-2">
                  Confidence Score: {Math.round(filing.ai_analysis.confidence_score * 100)}%
                </p>
              )}
            </div>
          )}

          {/* Key Financial Data */}
          {filing.ai_analysis.key_data && (
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-300 mb-3">Key Financial Data</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-xs">
                {/* Render amounts */}
                {filing.ai_analysis.key_data.amounts && filing.ai_analysis.key_data.amounts.map((amount: string, index: number) => {
                  const bgColor = amount.includes('buyback') || amount.includes('repurchase') ? 'bg-green-500/20' :
                                  amount.includes('dividend') ? 'bg-blue-500/20' : 'bg-purple-500/20';
                  const textColor = amount.includes('buyback') || amount.includes('repurchase') ? 'text-green-300' :
                                    amount.includes('dividend') ? 'text-blue-300' : 'text-purple-300';
                  const borderColor = amount.includes('buyback') || amount.includes('repurchase') ? 'border-green-500/30' :
                                      amount.includes('dividend') ? 'border-blue-500/30' : 'border-purple-500/30';
                  return (
                    <div key={`amount-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-2 border`}>
                      <span className={`font-medium ${textColor}`}>Amount</span>
                      <div className="text-white mt-1 font-semibold">{amount}</div>
                    </div>
                  );
                })}

                {/* Render percentages */}
                {filing.ai_analysis.key_data.percentages && filing.ai_analysis.key_data.percentages.map((percentage: string, index: number) => (
                  <div key={`percentage-${index}`} className="bg-orange-500/20 border-orange-500/30 rounded-lg p-2 border">
                    <span className="font-medium text-orange-300">Percentage</span>
                    <div className="text-white mt-1 font-semibold">{percentage}</div>
                  </div>
                ))}

                {/* Render dates */}
                {filing.ai_analysis.key_data.dates && filing.ai_analysis.key_data.dates.map((date: string, index: number) => {
                  const bgColor = date.includes('effective') || date.includes('end') ? 'bg-pink-500/20' : 'bg-indigo-500/20';
                  const textColor = date.includes('effective') || date.includes('end') ? 'text-pink-300' : 'text-indigo-300';
                  const borderColor = date.includes('effective') || date.includes('end') ? 'border-pink-500/30' : 'border-indigo-500/30';
                  return (
                    <div key={`date-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-2 border`}>
                      <span className={`font-medium ${textColor}`}>Date</span>
                      <div className="text-white mt-1 font-semibold">{date}</div>
                    </div>
                  );
                })}

                {/* Render financial metrics */}
                {filing.ai_analysis.key_data.financial_metrics && filing.ai_analysis.key_data.financial_metrics.map((metric: string, index: number) => (
                  <div key={`metric-${index}`} className="bg-yellow-500/20 border-yellow-500/30 rounded-lg p-2 border">
                    <span className="font-medium text-yellow-300">Financial Metric</span>
                    <div className="text-white mt-1 font-semibold">{metric}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Market Impact Predictions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Original Prediction */}
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-purple-200 mb-3 flex items-center">
                <Target className="w-4 h-4 mr-2" />
                Market Impact Prediction
              </h4>
              {filing.ai_analysis.market_impact ? (
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <div className="text-gray-400 mb-1">Price Movement</div>
                    <div className="text-white font-medium">{filing.ai_analysis.market_impact.price_movement_range}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Time Horizon</div>
                    <div className="text-white font-medium">{filing.ai_analysis.market_impact.time_horizon}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Volume Impact</div>
                    <div className="text-white font-medium">{filing.ai_analysis.market_impact.volume_impact}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Confidence</div>
                    <div className="text-white font-medium">{Math.round(filing.ai_analysis.market_impact.confidence_level * 100)}%</div>
                  </div>
                </div>
              ) : (
                <div className="text-gray-400 text-sm">No prediction data available</div>
              )}
            </div>

            {/* Actual Performance */}
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-200 mb-3 flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Actual Performance
              </h4>
              {filing.loading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                </div>
              ) : filing.priceComparison ? (
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <div className="text-gray-400 mb-1">Price at Disclosure</div>
                    <div className="text-white font-medium">{formatPrice(filing.priceComparison.historicalPrice)}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Current Price</div>
                    <div className="text-white font-medium">{formatPrice(filing.priceComparison.currentPrice)}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Actual Change</div>
                    <div className={`font-medium ${getPerformanceColor(filing.priceComparison.performance)}`}>
                      {formatPercentage(filing.priceComparison.percentChange)}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Last Updated</div>
                    <div className="text-white font-medium">
                      {format(new Date(filing.priceComparison.currentTimestamp), 'MM-dd HH:mm')}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-gray-400 text-sm">No price data available</div>
              )}
            </div>
          </div>

          {/* Multi-Company Impact Predictions */}
          {filing.ai_analysis.multi_company_impact && filing.ai_analysis.multi_company_impact.length > 1 && (
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                <Target className="w-4 h-4 mr-2" />
                Multi-Company Impact Predictions
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filing.ai_analysis.multi_company_impact.map((companyImpact, index) => (
                  <div key={index} className="bg-gray-600/30 rounded-lg p-3 border border-gray-600">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h5 className="text-sm font-medium text-white">{companyImpact.company_code}</h5>
                        <p className="text-xs text-gray-400">{companyImpact.company_name}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        companyImpact.role === 'primary'
                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                          : companyImpact.role === 'target'
                          ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                          : 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                      }`}>
                        {companyImpact.role.charAt(0).toUpperCase() + companyImpact.role.slice(1)}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <div className="text-gray-400 mb-1">Price Movement</div>
                        <div className="text-white font-medium">{companyImpact.price_movement_range}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Volume Impact</div>
                        <div className="text-white font-medium">{companyImpact.volume_impact}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Time Horizon</div>
                        <div className="text-white font-medium">{companyImpact.time_horizon}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Confidence</div>
                        <div className="text-white font-medium">{Math.round(companyImpact.confidence_level * 100)}%</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
