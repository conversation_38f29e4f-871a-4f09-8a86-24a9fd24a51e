'use client';

import React, { useState } from 'react';
import { X, BarChart3, Code, TrendingUp, Target } from 'lucide-react';
import { Filing } from '@/types/filing';

interface DetailedAnalysisModalProps {
  filing: Filing;
  onClose: () => void;
}

export default function DetailedAnalysisModal({ filing, onClose }: DetailedAnalysisModalProps) {
  const [activeView, setActiveView] = useState<'structured' | 'json'>('structured');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex-1">
            <h2 className="text-xl font-bold text-white mb-2">
              {filing.ai_analysis?.title_english || filing.title_en || filing.title}
            </h2>
            <div className="flex items-center space-x-3">
              <span className="text-lg font-semibold text-blue-400">{filing.company_code}</span>
              <span className="text-sm text-gray-300">{filing.company_name}</span>
              {filing.ai_analysis?.impact_level && (
                <span className={`px-2 py-1 rounded text-xs font-medium border ${
                  filing.ai_analysis.impact_level === 'very_high'
                    ? 'bg-red-600/20 text-red-300 border-red-600/30'
                    : filing.ai_analysis.impact_level === 'high'
                    ? 'bg-red-500/20 text-red-400 border-red-500/30'
                    : filing.ai_analysis.impact_level === 'medium'
                    ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                    : filing.ai_analysis.impact_level === 'low'
                    ? 'bg-green-500/20 text-green-400 border-green-500/30'
                    : 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                }`}>
                  {filing.ai_analysis.impact_score !== undefined
                    ? `${filing.ai_analysis.impact_score}/10 (${filing.ai_analysis.impact_level.toUpperCase()})`
                    : `${filing.ai_analysis.impact_level.toUpperCase()} IMPACT`
                  }
                </span>
              )}
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex items-center space-x-2 mr-4">
            <button
              onClick={() => setActiveView('structured')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                activeView === 'structured'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline mr-1" />
              Structured
            </button>
            <button
              onClick={() => setActiveView('json')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                activeView === 'json'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <Code className="w-4 h-4 inline mr-1" />
              JSON
            </button>
          </div>

          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-md transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeView === 'structured' ? (
            <StructuredAnalysisView filing={filing} />
          ) : (
            <JsonAnalysisView filing={filing} />
          )}
        </div>
      </div>
    </div>
  );
}

function StructuredAnalysisView({ filing }: { filing: Filing }) {
  const analysis = filing.ai_analysis;

  if (!analysis) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">No AI analysis available for this filing.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      <div className="bg-gray-700/50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-3">Executive Summary</h3>
        <p className="text-gray-300 leading-relaxed">{analysis.summary}</p>
      </div>

      {/* Business Context & Investor Implications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {analysis.business_context && (
          <div className="bg-gray-700/50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-3">Business Context</h3>
            <p className="text-gray-300 leading-relaxed">{analysis.business_context}</p>
          </div>
        )}

        {analysis.investor_implications && (
          <div className="bg-gray-700/50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-3">Investor Implications</h3>
            <p className="text-gray-300 leading-relaxed">{analysis.investor_implications}</p>
          </div>
        )}
      </div>

      {/* Key Financial Data */}
      {analysis.key_data && (
        <div className="bg-gray-700/50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-4">Key Financial Data</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Render amounts */}
            {analysis.key_data.amounts && analysis.key_data.amounts.map((amount: string, index: number) => {
              const { bgColor, textColor, borderColor } = getKeyDataColors('amounts', amount);
              return (
                <div key={`amount-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-3 border`}>
                  <span className={`font-medium ${textColor}`}>Amount</span>
                  <div className="text-white mt-1 text-sm font-semibold">{amount}</div>
                </div>
              );
            })}

            {/* Render percentages */}
            {analysis.key_data.percentages && analysis.key_data.percentages.map((percentage: string, index: number) => {
              const { bgColor, textColor, borderColor } = getKeyDataColors('percentages', percentage);
              return (
                <div key={`percentage-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-3 border`}>
                  <span className={`font-medium ${textColor}`}>Percentage</span>
                  <div className="text-white mt-1 text-sm font-semibold">{percentage}</div>
                </div>
              );
            })}

            {/* Render dates */}
            {analysis.key_data.dates && analysis.key_data.dates.map((date: string, index: number) => {
              const { bgColor, textColor, borderColor } = getKeyDataColors('dates', date);
              return (
                <div key={`date-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-3 border`}>
                  <span className={`font-medium ${textColor}`}>Date</span>
                  <div className="text-white mt-1 text-sm font-semibold">{date}</div>
                </div>
              );
            })}

            {/* Render financial metrics */}
            {analysis.key_data.financial_metrics && analysis.key_data.financial_metrics.map((metric: string, index: number) => {
              const { bgColor, textColor, borderColor } = getKeyDataColors('financial_metrics', metric);
              return (
                <div key={`metric-${index}`} className={`${bgColor} ${borderColor} rounded-lg p-3 border`}>
                  <span className={`font-medium ${textColor}`}>Financial Metric</span>
                  <div className="text-white mt-1 text-sm font-semibold">{metric}</div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Market Impact Prediction */}
      {analysis.market_impact && (
        <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-4 border border-purple-700">
          <div className="flex items-center mb-4">
            <TrendingUp className="h-5 w-5 text-purple-400 mr-2" />
            <h3 className="text-lg font-semibold text-purple-200">Market Impact Prediction</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-800/50 rounded p-3">
              <div className="text-gray-400 mb-1 text-sm">Price Movement</div>
              <div className="text-white font-medium">{analysis.market_impact.price_movement_range}</div>
            </div>
            <div className="bg-gray-800/50 rounded p-3">
              <div className="text-gray-400 mb-1 text-sm">Volume Impact</div>
              <div className="text-white font-medium">{analysis.market_impact.volume_impact}</div>
            </div>
            <div className="bg-gray-800/50 rounded p-3">
              <div className="text-gray-400 mb-1 text-sm">Time Horizon</div>
              <div className="text-white font-medium">{analysis.market_impact.time_horizon}</div>
            </div>
            <div className="bg-gray-800/50 rounded p-3">
              <div className="text-gray-400 mb-1 text-sm">Confidence</div>
              <div className="text-white font-medium">{Math.round(analysis.market_impact.confidence_level * 100)}%</div>
            </div>
          </div>
        </div>
      )}

      {/* Multi-Company Impact Predictions */}
      {analysis.multi_company_impact && analysis.multi_company_impact.length > 1 && (
        <div className="bg-gradient-to-r from-indigo-900/50 to-purple-900/50 rounded-lg p-4 border border-indigo-700">
          <div className="flex items-center mb-4">
            <Target className="h-5 w-5 text-indigo-400 mr-2" />
            <h3 className="text-lg font-semibold text-indigo-200">Multi-Company Impact Predictions</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {analysis.multi_company_impact.map((companyImpact, index) => (
              <div key={index} className="bg-gray-800/50 rounded-lg p-4 border border-gray-600">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h5 className="text-sm font-medium text-white">{companyImpact.company_code}</h5>
                    <p className="text-xs text-gray-400">{companyImpact.company_name}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    companyImpact.role === 'primary'
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                      : companyImpact.role === 'target'
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                      : 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                  }`}>
                    {companyImpact.role.charAt(0).toUpperCase() + companyImpact.role.slice(1)}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="text-gray-400 mb-1">Price Movement</div>
                    <div className="text-white font-medium">{companyImpact.price_movement_range}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Volume Impact</div>
                    <div className="text-white font-medium">{companyImpact.volume_impact}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Time Horizon</div>
                    <div className="text-white font-medium">{companyImpact.time_horizon}</div>
                  </div>
                  <div>
                    <div className="text-gray-400 mb-1">Confidence</div>
                    <div className="text-white font-medium">{Math.round(companyImpact.confidence_level * 100)}%</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function JsonAnalysisView({ filing }: { filing: Filing }) {
  return (
    <div className="bg-gray-900 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-white mb-4">Complete Filing Data (JSON)</h3>
      <pre className="text-sm text-gray-300 overflow-auto max-h-96 whitespace-pre-wrap">
        {JSON.stringify(filing, null, 2)}
      </pre>
    </div>
  );
}

function getKeyDataColors(key: string, value: string): { bgColor: string; textColor: string; borderColor: string } {
  // Determine sentiment from value for dynamic coloring
  const isPositive = value && (
    value.includes('増') || value.includes('increase') || value.includes('上昇') ||
    value.includes('gain') || value.includes('profit') || value.includes('dividend') ||
    value.includes('buyback') || value.includes('repurchase')
  );
  const isNegative = value && (
    value.includes('減') || value.includes('decrease') || value.includes('下降') ||
    value.includes('loss') || value.includes('cut') || value.includes('削減')
  );

  // Base colors by data type
  const colorMap: Record<string, { bgColor: string; textColor: string; borderColor: string }> = {
    amounts: {
      bgColor: value.includes('buyback') || value.includes('repurchase') ? 'bg-green-500/20' :
               value.includes('dividend') ? 'bg-blue-500/20' : 'bg-purple-500/20',
      textColor: value.includes('buyback') || value.includes('repurchase') ? 'text-green-300' :
                 value.includes('dividend') ? 'text-blue-300' : 'text-purple-300',
      borderColor: value.includes('buyback') || value.includes('repurchase') ? 'border-green-500/30' :
                   value.includes('dividend') ? 'border-blue-500/30' : 'border-purple-500/30'
    },
    percentages: {
      bgColor: 'bg-orange-500/20',
      textColor: 'text-orange-300',
      borderColor: 'border-orange-500/30'
    },
    dates: {
      bgColor: value.includes('effective') || value.includes('end') ? 'bg-pink-500/20' : 'bg-indigo-500/20',
      textColor: value.includes('effective') || value.includes('end') ? 'text-pink-300' : 'text-indigo-300',
      borderColor: value.includes('effective') || value.includes('end') ? 'border-pink-500/30' : 'border-indigo-500/30'
    },
    financial_metrics: {
      bgColor: isNegative ? 'bg-red-500/20' : 'bg-yellow-500/20',
      textColor: isNegative ? 'text-red-300' : 'text-yellow-300',
      borderColor: isNegative ? 'border-red-500/30' : 'border-yellow-500/30'
    },
    // Legacy support for old data structure
    dividend_amount: {
      bgColor: isNegative ? 'bg-red-500/20' : 'bg-blue-500/20',
      textColor: isNegative ? 'text-red-300' : 'text-blue-300',
      borderColor: isNegative ? 'border-red-500/30' : 'border-blue-500/30'
    },
    buyback_amount: {
      bgColor: 'bg-green-500/20',
      textColor: 'text-green-300',
      borderColor: 'border-green-500/30'
    },
    revenue: {
      bgColor: isNegative ? 'bg-red-500/20' : 'bg-purple-500/20',
      textColor: isNegative ? 'text-red-300' : 'text-purple-300',
      borderColor: isNegative ? 'border-red-500/30' : 'border-purple-500/30'
    },
    profit: {
      bgColor: isNegative ? 'bg-red-500/20' : 'bg-yellow-500/20',
      textColor: isNegative ? 'text-red-300' : 'text-yellow-300',
      borderColor: isNegative ? 'border-red-500/30' : 'border-yellow-500/30'
    }
  };

  return colorMap[key] || {
    bgColor: 'bg-gray-500/20',
    textColor: 'text-gray-300',
    borderColor: 'border-gray-500/30'
  };
}
