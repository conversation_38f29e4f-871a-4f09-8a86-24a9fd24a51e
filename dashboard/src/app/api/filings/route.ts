import { NextRequest, NextResponse } from 'next/server';
import { Firestore } from '@google-cloud/firestore';

// Initialize Firestore with the same project as the Cloud Function
const firestore = new Firestore({
  projectId: 'tokyotickers',
  // Use default service account in Cloud Run environment
  // This will work in local development if you have gcloud auth configured
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const category = searchParams.get('category');
    const impactLevel = searchParams.get('impactLevel');
    const company = searchParams.get('company');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Try to connect to Firestore and get real TDnet data

    // Build Firestore query - simplified to avoid composite index requirements
    // Use higher multiplier when filtering by category to ensure we get enough results
    const fetchMultiplier = category && category !== 'all' ? 10 : 3;
    let query = firestore.collection('tdnet_filings')
      .orderBy('disclosure_datetime', 'desc')
      .limit(limit * fetchMultiplier); // Get more to filter in memory

    // Execute query
    const snapshot = await query.get();
    let filings = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      // Convert Firestore timestamp to ISO string
      disclosure_datetime: doc.data().disclosure_datetime?.toDate?.()?.toISOString() || doc.data().disclosure_datetime,
      processed_timestamp: doc.data().processed_timestamp?.toDate?.()?.toISOString() || doc.data().processed_timestamp,
    }));

    // Apply filters in memory to avoid Firestore composite index requirements
    if (category && category !== 'all') {
      filings = filings.filter(filing => filing.ai_analysis?.category === category);
    }

    if (impactLevel && impactLevel !== 'all') {
      // Support multiple impact levels separated by comma (e.g., "medium,high")
      const levels = impactLevel.split(',').map(level => level.trim());
      filings = filings.filter(filing =>
        filing.ai_analysis?.impact_level && levels.includes(filing.ai_analysis.impact_level)
      );
    }

    if (company) {
      filings = filings.filter(filing => filing.company_code === company);
    }

    if (startDate) {
      const start = new Date(startDate);
      filings = filings.filter(filing => new Date(filing.disclosure_datetime) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      filings = filings.filter(filing => new Date(filing.disclosure_datetime) <= end);
    }

    // Limit final results
    filings = filings.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: filings,
      count: filings.length,
    });

  } catch (error) {
    console.error('Error fetching filings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch filings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, filingId } = body;

    if (action === 'regenerate_analysis' && filingId) {
      // Call the Cloud Function to regenerate analysis for this specific filing
      const cloudFunctionUrl = 'https://asia-northeast1-tokyotickers.cloudfunctions.net/tdnet-scraper';

      const response = await fetch(cloudFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'regenerate_single_analysis',
          filing_id: filingId
        })
      });

      if (!response.ok) {
        throw new Error(`Cloud Function returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      return NextResponse.json({
        success: true,
        message: 'Analysis regeneration initiated',
        result: result
      });
    }

    // Default: Add a new filing (for testing purposes)
    const docRef = await firestore.collection('tdnet_filings').add({
      ...body,
      processed_timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      id: docRef.id,
    });

  } catch (error) {
    console.error('Error in POST request:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
