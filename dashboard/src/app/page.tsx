'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Search, Filter, Calendar, Clock, TrendingUp, FileText, AlertCircle, BarChart3, Target, MoreVertical, Star, RefreshCw, Heart } from 'lucide-react';
import { format } from 'date-fns';
import ShareBuybackAnalysis from '@/components/ShareBuybackAnalysis';
import ImpactTrackingComponent from '@/components/ImpactTracking';
import DetailedAnalysisModal from '@/components/DetailedAnalysisModal';
import LoginPage from '@/components/LoginPage';
import UserMenu from '@/components/UserMenu';
import { useAuth } from '@/contexts/AuthContext';
import { isFirebaseConfigured } from '@/lib/firebase';
import { Filing } from '@/types/filing';

const CATEGORIES = [
  { value: 'all', label: 'All Categories' },
  { value: 'dividend', label: 'Dividend' },
  { value: 'share_buyback', label: 'Share Buyback' },
  { value: 'earnings', label: 'Earnings' },
  { value: 'guidance_revision', label: 'Guidance Revision' },
  { value: 'merger_acquisition', label: 'M&A' },
  { value: 'stock_split', label: 'Stock Split' },
  { value: 'other', label: 'Other' },
];

const IMPACT_LEVELS = [
  { value: 'all', label: 'All Impact Levels' },
  { value: 'very_high', label: 'Very High (9-10)' },
  { value: 'high', label: 'High (7-8)' },
  { value: 'medium', label: 'Medium (5-6)' },
  { value: 'low', label: 'Low (3-4)' },
  { value: 'very_low', label: 'Very Low (0-2)' },
];

const CONFIDENCE_LEVELS = [
  { value: 'all', label: 'All Confidence Levels' },
  { value: '0.9-1.0', label: 'Very High (0.9-1.0)' },
  { value: '0.8-0.9', label: 'High (0.8-0.9)' },
  { value: '0.7-0.8', label: 'Medium (0.7-0.8)' },
  { value: '0.6-0.7', label: 'Low (0.6-0.7)' },
  { value: '0.0-0.6', label: 'Very Low (0.0-0.6)' },
];

const PRICE_MOVEMENT_RANGES = [
  { value: 'all', label: 'All Price Movements' },
  { value: 'positive_high', label: 'High Positive (+10% to +50%)' },
  { value: 'positive_medium', label: 'Medium Positive (+5% to +10%)' },
  { value: 'positive_low', label: 'Low Positive (+1% to +5%)' },
  { value: 'neutral', label: 'Neutral (-1% to +1%)' },
  { value: 'negative_low', label: 'Low Negative (-5% to -1%)' },
  { value: 'negative_medium', label: 'Medium Negative (-10% to -5%)' },
  { value: 'negative_high', label: 'High Negative (-50% to -10%)' },
];

const getCategoryColor = (category: string) => {
  const colors = {
    dividend: 'bg-green-500/20 text-green-400',
    share_buyback: 'bg-blue-500/20 text-blue-400',
    earnings: 'bg-purple-500/20 text-purple-400',
    guidance_revision: 'bg-orange-500/20 text-orange-400',
    merger_acquisition: 'bg-red-500/20 text-red-400',
    stock_split: 'bg-yellow-500/20 text-yellow-400',
    other: 'bg-gray-500/20 text-gray-400',
  };
  return colors[category as keyof typeof colors] || colors.other;
};

const getImpactLevelColor = (impactLevel: string) => {
  const colors = {
    very_high: 'bg-red-600/20 text-red-300 border-red-600/30',
    high: 'bg-red-500/20 text-red-400 border-red-500/30',
    medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    low: 'bg-green-500/20 text-green-400 border-green-500/30',
    very_low: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
  };
  return colors[impactLevel as keyof typeof colors] || 'bg-gray-500/20 text-gray-400 border-gray-500/30';
};

const TABS = [
  { id: 'all-filings', label: 'All Filings', icon: FileText },
  { id: 'favorites', label: 'Favorites', icon: Heart },
  { id: 'buyback-analysis', label: 'Share Buyback Analysis', icon: BarChart3 },
  { id: 'impact-tracking', label: 'Impact Tracking', icon: Target },
];

export default function Dashboard() {
  const { user, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('all-filings');
  const [filings, setFilings] = useState<Filing[]>([]);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetailModal, setShowDetailModal] = useState<Filing | null>(null);
  const [currentTime, setCurrentTime] = useState<string>('');
  const [filters, setFilters] = useState({
    category: 'all',
    impactLevel: 'all',
    search: '',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    confidenceLevel: 'all',
    priceMovementRange: 'all',
  });

  // Update current time every second (client-side only)
  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(new Date().toLocaleTimeString());
    };

    // Set initial time
    updateTime();

    // Update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  // Show loading screen while checking authentication (only if Firebase is configured)
  if (isFirebaseConfigured && authLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mb-4"></div>
          <p className="text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login page if Firebase is configured but not authenticated
  if (isFirebaseConfigured && !user) {
    return <LoginPage />;
  }

  const fetchFilings = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (filters.category !== 'all') params.append('category', filters.category);
      if (filters.impactLevel !== 'all') params.append('impactLevel', filters.impactLevel);
      if (filters.search) params.append('company', filters.search);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);

      const response = await fetch(`/api/filings?${params}`);
      const data = await response.json();

      if (data.success) {
        setFilings(data.data);
        setError(null);
      } else {
        setError(data.error || 'Failed to fetch filings');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching filings:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchFilings();
  }, [fetchFilings]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Load favorites from localStorage on mount
  useEffect(() => {
    const savedFavorites = localStorage.getItem('tdnet-favorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }
  }, []);

  // Save favorites to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('tdnet-favorites', JSON.stringify(Array.from(favorites)));
  }, [favorites]);

  const toggleFavorite = (filingId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(filingId)) {
        newFavorites.delete(filingId);
      } else {
        newFavorites.add(filingId);
      }
      return newFavorites;
    });
  };

  const regenerateAnalysis = async (filingId: string) => {
    try {
      const response = await fetch('/api/filings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'regenerate_analysis',
          filingId: filingId
        })
      });

      if (response.ok) {
        // Refresh the filings list to show updated analysis
        fetchFilings();
      } else {
        console.error('Failed to regenerate analysis');
      }
    } catch (error) {
      console.error('Error regenerating analysis:', error);
    }
  };

  // Enhanced client-side filtering
  const filteredFilings = useMemo(() => {
    let filtered = filings;

    // Apply favorite filter first
    if (activeTab === 'favorites') {
      filtered = filtered.filter(filing => favorites.has(filing.id));
    }

    // Apply additional client-side filters
    return filtered.filter(filing => {
      // Confidence level filter
      if (filters.confidenceLevel !== 'all') {
        const confidence = filing.ai_analysis?.market_impact?.confidence_level;
        if (confidence === undefined) return false;

        switch (filters.confidenceLevel) {
          case '0.9-1.0':
            if (confidence < 0.9) return false;
            break;
          case '0.8-0.9':
            if (confidence < 0.8 || confidence >= 0.9) return false;
            break;
          case '0.7-0.8':
            if (confidence < 0.7 || confidence >= 0.8) return false;
            break;
          case '0.6-0.7':
            if (confidence < 0.6 || confidence >= 0.7) return false;
            break;
          case '0.0-0.6':
            if (confidence >= 0.6) return false;
            break;
        }
      }

      // Price movement range filter
      if (filters.priceMovementRange !== 'all') {
        const priceMovement = filing.ai_analysis?.market_impact?.price_movement_range;
        if (!priceMovement) return false;

        // Extract percentage from price movement range (e.g., "+5% to +15%" -> 10)
        const percentMatch = priceMovement.match(/([+-]?\d+(?:\.\d+)?)%/g);
        if (!percentMatch) return false;

        const percentages = percentMatch.map(p => parseFloat(p.replace('%', '')));
        const avgPercent = percentages.reduce((a, b) => a + b, 0) / percentages.length;

        switch (filters.priceMovementRange) {
          case 'positive_high':
            if (avgPercent < 10 || avgPercent > 50) return false;
            break;
          case 'positive_medium':
            if (avgPercent < 5 || avgPercent >= 10) return false;
            break;
          case 'positive_low':
            if (avgPercent < 1 || avgPercent >= 5) return false;
            break;
          case 'neutral':
            if (avgPercent < -1 || avgPercent > 1) return false;
            break;
          case 'negative_low':
            if (avgPercent > -1 || avgPercent <= -5) return false;
            break;
          case 'negative_medium':
            if (avgPercent > -5 || avgPercent <= -10) return false;
            break;
          case 'negative_high':
            if (avgPercent > -10 || avgPercent < -50) return false;
            break;
        }
      }

      // Enhanced date/time filters
      if (filters.startDate || filters.startTime) {
        const filingDate = new Date(filing.disclosure_datetime);

        if (filters.startDate) {
          const startDate = new Date(filters.startDate);
          if (filters.startTime) {
            const [hours, minutes] = filters.startTime.split(':').map(Number);
            startDate.setHours(hours, minutes, 0, 0);
          } else {
            startDate.setHours(0, 0, 0, 0);
          }
          if (filingDate < startDate) return false;
        }
      }

      if (filters.endDate || filters.endTime) {
        const filingDate = new Date(filing.disclosure_datetime);

        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          if (filters.endTime) {
            const [hours, minutes] = filters.endTime.split(':').map(Number);
            endDate.setHours(hours, minutes, 59, 999);
          } else {
            endDate.setHours(23, 59, 59, 999);
          }
          if (filingDate > endDate) return false;
        }
      }

      return true;
    });
  }, [filings, filters, favorites, activeTab]);

  const getDisplayedFilings = () => {
    return filteredFilings;
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 shadow-sm border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-400 mr-3" />
              <h1 className="text-2xl font-bold text-white">TDnet Real-time Analysis</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-300">
                Last updated: {currentTime}
              </div>
              {isFirebaseConfigured && <UserMenu />}
              {!isFirebaseConfigured && (
                <div className="text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded border border-yellow-400/30">
                  Demo Mode - No Authentication
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {TABS.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'buyback-analysis' ? (
          <ShareBuybackAnalysis />
        ) : activeTab === 'impact-tracking' ? (
          <ImpactTrackingComponent />
        ) : (
          <>
            {/* Enhanced Filters */}
        <div className="bg-gray-800 rounded-lg shadow p-6 mb-6 border border-gray-700">
          <div className="space-y-4">
            {/* First Row - Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search by company code..."
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>

              {/* Category Filter */}
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  {CATEGORIES.map(cat => (
                    <option key={cat.value} value={cat.value} className="bg-gray-700 text-white">{cat.label}</option>
                  ))}
                </select>
              </div>

              {/* Impact Level Filter */}
              <div className="relative">
                <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.impactLevel}
                  onChange={(e) => handleFilterChange('impactLevel', e.target.value)}
                >
                  {IMPACT_LEVELS.map(level => (
                    <option key={level.value} value={level.value} className="bg-gray-700 text-white">{level.label}</option>
                  ))}
                </select>
              </div>

              {/* Confidence Level Filter */}
              <div className="relative">
                <Target className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.confidenceLevel}
                  onChange={(e) => handleFilterChange('confidenceLevel', e.target.value)}
                >
                  {CONFIDENCE_LEVELS.map(level => (
                    <option key={level.value} value={level.value} className="bg-gray-700 text-white">{level.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Second Row - Date/Time and Price Movement Filters */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {/* Start Date */}
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange('startDate', e.target.value)}
                />
              </div>

              {/* Start Time */}
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="time"
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.startTime}
                  onChange={(e) => handleFilterChange('startTime', e.target.value)}
                />
              </div>

              {/* End Date */}
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange('endDate', e.target.value)}
                />
              </div>

              {/* End Time */}
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="time"
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.endTime}
                  onChange={(e) => handleFilterChange('endTime', e.target.value)}
                />
              </div>

              {/* Price Movement Range Filter */}
              <div className="relative">
                <BarChart3 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  className="pl-10 pr-4 py-2 w-full bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.priceMovementRange}
                  onChange={(e) => handleFilterChange('priceMovementRange', e.target.value)}
                >
                  {PRICE_MOVEMENT_RANGES.map(range => (
                    <option key={range.value} value={range.value} className="bg-gray-700 text-white">{range.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-900 border border-red-700 rounded-md p-4 mb-6">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-200">Error</h3>
                <p className="text-sm text-red-300 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
            <p className="mt-2 text-gray-300">Loading filings...</p>
          </div>
        )}

        {/* Filings List */}
        {!loading && !error && (
          <div className="space-y-4">
            {activeTab === 'favorites' && favorites.size === 0 ? (
              <div className="text-center py-12">
                <Heart className="mx-auto h-12 w-12 text-gray-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-200">No favorite filings</h3>
                <p className="mt-1 text-sm text-gray-400">Star filings to add them to your favorites</p>
              </div>
            ) : getDisplayedFilings().length === 0 ? (
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-200">No filings found</h3>
                <p className="mt-1 text-sm text-gray-400">Try adjusting your filters</p>
              </div>
            ) : (
              getDisplayedFilings().map((filing) => (
                <FilingCard
                  key={filing.id}
                  filing={filing}
                  isFavorite={favorites.has(filing.id)}
                  onToggleFavorite={toggleFavorite}
                  onRegenerateAnalysis={regenerateAnalysis}
                  onShowDetails={setShowDetailModal}
                />
              ))
            )}
          </div>
        )}
          </>
        )}
      </div>

      {/* Detailed Analysis Modal */}
      {showDetailModal && (
        <DetailedAnalysisModal
          filing={showDetailModal}
          onClose={() => setShowDetailModal(null)}
        />
      )}
    </div>
  );
}

function FilingCard({
  filing,
  isFavorite,
  onToggleFavorite,
  onRegenerateAnalysis,
  onShowDetails
}: {
  filing: Filing;
  isFavorite: boolean;
  onToggleFavorite: (filingId: string) => void;
  onRegenerateAnalysis: (filingId: string) => void;
  onShowDetails: (filing: Filing) => void;
}) {
  const [showMenu, setShowMenu] = useState(false);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setShowMenu(false);
    if (showMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showMenu]);

  return (
    <div className="bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow p-6 border border-gray-700">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <span className="text-lg font-semibold text-white">
              {filing.company_code}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(filing.ai_analysis?.category || 'other')}`}>
              {filing.ai_analysis?.category || 'Other'}
            </span>
            {filing.ai_analysis?.impact_level && (
              <span className={`px-2 py-1 rounded text-xs font-medium border ${getImpactLevelColor(filing.ai_analysis.impact_level)}`}>
                {filing.ai_analysis.impact_score !== undefined
                  ? `${filing.ai_analysis.impact_score}/10 (${filing.ai_analysis.impact_level.toUpperCase()})`
                  : `${filing.ai_analysis.impact_level.toUpperCase()} IMPACT`
                }
              </span>
            )}
          </div>
          <div className="mb-2">
            <h3 className="text-lg font-medium text-white mb-1">
              {filing.ai_analysis?.title_english || filing.title_en || filing.title}
            </h3>
            {filing.title !== (filing.ai_analysis?.title_english || filing.title_en) && (
              <p className="text-sm text-gray-300 italic">
                {filing.title}
              </p>
            )}
          </div>
          <p className="text-sm text-gray-400">
            {format(new Date(filing.disclosure_datetime), 'PPpp')}
          </p>
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-2 ml-4">
          {/* Favorite Star */}
          <button
            onClick={() => onToggleFavorite(filing.id)}
            className={`p-2 rounded-md transition-colors ${
              isFavorite
                ? 'text-yellow-400 hover:text-yellow-300 bg-yellow-400/10'
                : 'text-gray-400 hover:text-yellow-400 hover:bg-yellow-400/10'
            }`}
            title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          >
            <Star className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
          </button>

          {/* View Full Analysis button - moved next to favorite */}
          <button
            onClick={() => onShowDetails(filing)}
            className="p-2 rounded-md text-gray-400 hover:text-blue-400 hover:bg-blue-400/10 transition-colors"
            title="View Full Analysis"
          >
            <BarChart3 className="h-4 w-4" />
          </button>

          {/* Three-dot menu */}
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-300 hover:bg-gray-700 transition-colors"
              title="More options"
            >
              <MoreVertical className="h-4 w-4" />
            </button>

            {showMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-gray-700 rounded-md shadow-lg border border-gray-600 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onRegenerateAnalysis(filing.id);
                      setShowMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-200 hover:bg-gray-600 transition-colors"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate Analysis
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* View PDF button */}
          <a
            href={filing.pdf_url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-2 border border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-200 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <FileText className="h-4 w-4 mr-1" />
            View PDF
          </a>
        </div>
      </div>

      {/* AI Analysis */}
      {filing.ai_analysis && (
        <div className="border-t border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-white mb-2">AI Analysis Summary</h4>
          <p className="text-sm text-gray-300 mb-3">
            {typeof filing.ai_analysis.summary === 'string' ?
              filing.ai_analysis.summary.replace(/```json|```/g, '').trim() :
              JSON.stringify(filing.ai_analysis.summary)
            }
          </p>



          {filing.ai_analysis.key_data && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-xs">
              {filing.ai_analysis.key_data.dividend_amount && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-blue-400">Dividend</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.dividend_amount}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.buyback_amount && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-green-400">Buyback Amount</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.buyback_amount}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.revenue && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-purple-400">Revenue</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.revenue}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.profit && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-yellow-400">Profit</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.profit}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.dividend_yield && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-cyan-400">Dividend Yield</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.dividend_yield}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.shares_to_buyback && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-orange-400">Shares to Buyback</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.shares_to_buyback}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.effective_date && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-pink-400">Effective Date</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.effective_date}</div>
                </div>
              )}

              {filing.ai_analysis.key_data.announcement_date && (
                <div className="bg-gray-700/50 rounded p-2">
                  <span className="font-medium text-indigo-400">Announcement Date</span>
                  <div className="text-white mt-1">{filing.ai_analysis.key_data.announcement_date}</div>
                </div>
              )}
            </div>
          )}

          {/* Quantitative Market Impact Prediction */}
          {filing.ai_analysis.market_impact ? (
            <div className="mt-3 p-4 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-md border border-purple-700">
              <div className="flex items-center mb-3">
                <TrendingUp className="h-4 w-4 text-purple-400 mr-2" />
                <span className="text-sm font-medium text-purple-200">Market Impact Prediction</span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                <div className="bg-gray-800/50 rounded p-2">
                  <div className="text-gray-400 mb-1">Price Movement</div>
                  <div className="text-white font-medium">{filing.ai_analysis.market_impact.price_movement_range}</div>
                </div>
                <div className="bg-gray-800/50 rounded p-2">
                  <div className="text-gray-400 mb-1">Volume Impact</div>
                  <div className="text-white font-medium">{filing.ai_analysis.market_impact.volume_impact}</div>
                </div>
                <div className="bg-gray-800/50 rounded p-2">
                  <div className="text-gray-400 mb-1">Time Horizon</div>
                  <div className="text-white font-medium">{filing.ai_analysis.market_impact.time_horizon}</div>
                </div>
                <div className="bg-gray-800/50 rounded p-2">
                  <div className="text-gray-400 mb-1">Confidence</div>
                  <div className="text-white font-medium">{Math.round(filing.ai_analysis.market_impact.confidence_level * 100)}%</div>
                </div>
              </div>
            </div>
          ) : filing.ai_analysis.impact_assessment && (
            <div className="mt-3 p-3 bg-blue-900 rounded-md border border-blue-800">
              <span className="text-sm font-medium text-blue-200">Impact Assessment:</span>
              <p className="text-sm text-blue-300 mt-1">
                {typeof filing.ai_analysis.impact_assessment === 'string' ?
                  filing.ai_analysis.impact_assessment.replace(/```json|```/g, '').trim() :
                  JSON.stringify(filing.ai_analysis.impact_assessment)
                }
              </p>
            </div>
          )}

          {/* Multi-Company Impact Predictions */}
          {filing.ai_analysis.multi_company_impact && filing.ai_analysis.multi_company_impact.length > 1 && (
            <div className="mt-3 p-4 bg-gradient-to-r from-indigo-900/50 to-purple-900/50 rounded-md border border-indigo-700">
              <div className="flex items-center mb-3">
                <Target className="h-4 w-4 text-indigo-400 mr-2" />
                <span className="text-sm font-medium text-indigo-200">Multi-Company Impact Predictions</span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {filing.ai_analysis.multi_company_impact.map((companyImpact, index) => (
                  <div key={index} className="bg-gray-800/50 rounded-lg p-3 border border-gray-600">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h5 className="text-sm font-medium text-white">{companyImpact.company_code}</h5>
                        <p className="text-xs text-gray-400">{companyImpact.company_name}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        companyImpact.role === 'primary'
                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                          : companyImpact.role === 'target'
                          ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                          : 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                      }`}>
                        {companyImpact.role.charAt(0).toUpperCase() + companyImpact.role.slice(1)}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <div className="text-gray-400 mb-1">Price Movement</div>
                        <div className="text-white font-medium">{companyImpact.price_movement_range}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Volume Impact</div>
                        <div className="text-white font-medium">{companyImpact.volume_impact}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Time Horizon</div>
                        <div className="text-white font-medium">{companyImpact.time_horizon}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 mb-1">Confidence</div>
                        <div className="text-white font-medium">{Math.round(companyImpact.confidence_level * 100)}%</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}



