/**
 * Core types for TradingView Price Service
 */

export interface QuoteData {
  symbol: string;
  price: number;
  currency: string;
  timestamp: string;
  change: number;
  changePercent: number;
  volume: number;
  marketStatus: 'open' | 'closed' | 'pre-market' | 'after-hours' | 'unknown';
  source: 'tradingview';
  lastUpdate?: string;
}

export interface OHLCData {
  time: number; // Unix timestamp
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface OHLCResponse extends OHLCData {
  symbol: string;
  date: string;
  currency: string;
  exchange: string;
}

export interface ChartData {
  symbol: string;
  timeframe: string;
  periods: OHLCData[];
  currency: string;
  exchange: string;
  description: string;
  source: 'tradingview';
  lastUpdate: string;
}

export interface HistoricalPriceRequest {
  symbol: string;
  from: string; // ISO date string
  to: string;   // ISO date string
  timeframe: '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1D' | '1W' | '1M';
}

export interface PriceComparisonData {
  symbol: string;
  currentPrice: number;
  historicalPrice: number;
  change: number;
  changePercent: number;
  performance: 'gain' | 'loss' | 'neutral';
  currency: string;
  currentTimestamp: string;
  historicalDate: string;
  source: 'tradingview';
}

export interface BatchQuoteRequest {
  symbols: string[];
  includeExtendedHours?: boolean;
}

export interface BatchQuoteResponse {
  results: Array<{
    symbol: string;
    success: boolean;
    data: QuoteData | null;
    error: string | null;
  }>;
  timestamp: string;
  totalRequested: number;
  successCount: number;
  failureCount: number;
}

export interface MarketStatus {
  isOpen: boolean;
  nextOpen?: string;
  nextClose?: string;
  timezone: string;
  currentTime: string;
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  tradingViewConnection: boolean;
  authentication: boolean;
  cacheStatus: 'active' | 'disabled';
  uptime: number;
  version: string;
  timestamp: string;
  errors?: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  requestId?: string;
}

export interface TradingViewCredentials {
  username?: string;
  password?: string;
  sessionId?: string;
}

export interface ServiceConfig {
  port: number;
  nodeEnv: string;
  tradingView: TradingViewCredentials;
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  cache: {
    ttlSeconds: number;
    maxKeys: number;
  };
  timeouts: {
    connectionMs: number;
    requestMs: number;
    retryAttempts: number;
    retryDelayMs: number;
  };
  cors: {
    allowedOrigins: string[];
  };
  logging: {
    level: string;
    file?: string;
  };
}

export interface SymbolInfo {
  symbol: string;
  description: string;
  exchange: string;
  currency: string;
  type: string;
  country: string;
  sector?: string;
  industry?: string;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  movingAverages: {
    sma20: number;
    sma50: number;
    sma200: number;
    ema20: number;
    ema50: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
}

export interface ExtendedQuoteData extends QuoteData {
  dayHigh: number;
  dayLow: number;
  open: number;
  previousClose: number;
  marketCap?: number;
  pe?: number;
  eps?: number;
  dividend?: number;
  yield?: number;
  beta?: number;
  averageVolume?: number;
  technicals?: TechnicalIndicators;
}

export type TimeFrame = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1D' | '1W' | '1M';

export interface WebSocketMessage {
  type: 'quote' | 'chart' | 'error' | 'connected' | 'disconnected';
  symbol?: string;
  data?: any;
  error?: string;
  timestamp: string;
}
