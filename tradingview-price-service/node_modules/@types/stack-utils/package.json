{"name": "@types/stack-utils", "version": "2.0.3", "description": "TypeScript definitions for stack-utils", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/stack-utils"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ab51d155e7946b0b0e0edab741811a35172a48de2674195feb31eaaf7bf992b7", "typeScriptVersion": "4.5"}